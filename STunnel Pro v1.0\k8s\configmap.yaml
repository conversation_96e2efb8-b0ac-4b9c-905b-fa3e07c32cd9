apiVersion: v1
kind: ConfigMap
metadata:
  name: stunnel-pro-config
  namespace: stunnel-pro
data:
  config.yaml: |
    server:
      host: "0.0.0.0"
      port: 8080
      mode: "release"
      read_timeout: "30s"
      write_timeout: "30s"
      idle_timeout: "120s"
      tls:
        enabled: false

    database:
      host: "postgres-service"
      port: 5432
      user: "stunnel"
      name: "stunnel_pro"
      ssl_mode: "disable"
      max_open_conns: 25
      max_idle_conns: 5
      max_lifetime: "5m"

    redis:
      host: "redis-service"
      port: 6379
      password: ""
      db: 0
      pool_size: 10
      min_idle_conns: 2
      dial_timeout: "5s"
      read_timeout: "3s"
      write_timeout: "3s"

    security:
      password_min_length: 8
      max_login_attempts: 5
      lockout_duration: "30m"
      session_timeout: "24h"
      two_factor_enabled: false
      rate_limit_enabled: true
      rate_limit_requests: 100
      rate_limit_window: "1m"
      cors_allowed_origins:
        - "*"
      cors_allowed_methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "OPTIONS"
      cors_allowed_headers:
        - "*"
      cors_allow_credentials: true

    monitoring:
      enabled: true
      prometheus_enabled: true
      prometheus_port: 9090
      metrics_interval: "30s"
      health_check_path: "/health"
      log_level: "info"
      log_format: "json"
      log_output: "stdout"

    app:
      name: "UTunnel Pro"
      version: "2.0.0"
      environment: "production"
      debug: false
      timezone: "UTC"
      language: "en"
