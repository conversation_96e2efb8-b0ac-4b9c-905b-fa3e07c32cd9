# STunnel Pro v1.0 - Language Detection Configuration
# Created by SalehMonfared - https://github.com/SalehMonfared

# Go files (Backend)
*.go linguist-language=Go
backend/**/*.go linguist-language=Go
cmd/**/*.go linguist-language=Go
internal/**/*.go linguist-language=Go

# TypeScript/JavaScript files (Frontend)
*.ts linguist-language=TypeScript
*.tsx linguist-language=TypeScript
*.js linguist-language=JavaScript
*.jsx linguist-language=JavaScript
frontend/**/*.ts linguist-language=TypeScript
frontend/**/*.tsx linguist-language=TypeScript
frontend/**/*.js linguist-language=JavaScript
frontend/**/*.jsx linguist-language=JavaScript

# React/Next.js specific
frontend/src/**/*.tsx linguist-language=TypeScript
frontend/pages/**/*.tsx linguist-language=TypeScript
frontend/components/**/*.tsx linguist-language=TypeScript

# Configuration files
*.yaml linguist-language=YAML
*.yml linguist-language=YAML
*.json linguist-language=JSON
*.toml linguist-language=TOML
*.env linguist-language=Shell
*.env.* linguist-language=Shell

# Docker files
Dockerfile linguist-language=Dockerfile
Dockerfile.* linguist-language=Dockerfile
docker-compose*.yml linguist-language=YAML

# Kubernetes files
k8s/**/*.yaml linguist-language=YAML
k8s/**/*.yml linguist-language=YAML

# Shell scripts
*.sh linguist-language=Shell
scripts/**/*.sh linguist-language=Shell
install.sh linguist-language=Shell

# SQL files
*.sql linguist-language=SQL
migrations/**/*.sql linguist-language=SQL
backend/migrations/**/*.sql linguist-language=SQL

# Monitoring configurations
monitoring/**/*.yml linguist-language=YAML
monitoring/**/*.yaml linguist-language=YAML
monitoring/**/*.json linguist-language=JSON

# Documentation (exclude from language stats)
*.md linguist-documentation
docs/**/* linguist-documentation
README.md linguist-documentation
CHANGELOG.md linguist-documentation
CONTRIBUTING.md linguist-documentation
LICENSE linguist-documentation
AUTHORS linguist-documentation

# Exclude vendor/generated files from language stats
vendor/* linguist-vendored
node_modules/* linguist-vendored
*.min.js linguist-generated
*.min.css linguist-generated
dist/* linguist-generated
build/* linguist-generated
.next/* linguist-generated
coverage/* linguist-generated
*.coverage linguist-generated

# Go specific excludes
go.sum linguist-generated
*.pb.go linguist-generated

# Frontend build artifacts
frontend/dist/* linguist-generated
frontend/build/* linguist-generated
frontend/.next/* linguist-generated
frontend/out/* linguist-generated

# Test files (mark as test)
*_test.go linguist-language=Go
**/*_test.go linguist-language=Go
*.test.js linguist-language=JavaScript
*.test.ts linguist-language=TypeScript
*.spec.js linguist-language=JavaScript
*.spec.ts linguist-language=TypeScript

# Config files for tools
.gitignore linguist-language=gitignore
.dockerignore linguist-language=gitignore
.eslintrc* linguist-language=JSON
.prettierrc* linguist-language=JSON
tsconfig.json linguist-language=JSON
package.json linguist-language=JSON
package-lock.json linguist-generated
yarn.lock linguist-generated
go.mod linguist-language=Go
go.sum linguist-generated

# IDE/Editor files (exclude)
.vscode/* linguist-vendored
.idea/* linguist-vendored
*.swp linguist-vendored
*.swo linguist-vendored
*~ linguist-vendored

# OS files (exclude)
.DS_Store linguist-vendored
Thumbs.db linguist-vendored
desktop.ini linguist-vendored

# Log files (exclude)
*.log linguist-vendored
logs/* linguist-vendored

# Temporary files (exclude)
tmp/* linguist-vendored
temp/* linguist-vendored
*.tmp linguist-vendored

# Binary files (exclude)
*.exe linguist-vendored
*.dll linguist-vendored
*.so linguist-vendored
*.dylib linguist-vendored

# Archive files (exclude)
*.zip linguist-vendored
*.tar.gz linguist-vendored
*.rar linguist-vendored
*.7z linguist-vendored

# Image files (exclude from language detection)
*.png linguist-vendored
*.jpg linguist-vendored
*.jpeg linguist-vendored
*.gif linguist-vendored
*.svg linguist-vendored
*.ico linguist-vendored
*.webp linguist-vendored

# Font files (exclude)
*.woff linguist-vendored
*.woff2 linguist-vendored
*.ttf linguist-vendored
*.eot linguist-vendored

# Certificate files (exclude)
*.pem linguist-vendored
*.crt linguist-vendored
*.key linguist-vendored
*.p12 linguist-vendored

# Database files (exclude)
*.db linguist-vendored
*.sqlite linguist-vendored
*.sqlite3 linguist-vendored
