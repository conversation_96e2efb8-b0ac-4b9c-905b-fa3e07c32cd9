MIT License

Copyright (c) 2024 SalehMonfared

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

STunnel Pro v1.0 - Advanced Tunnel Management System
Created by SalehMonfared
Repository: https://github.com/SalehMonfared/stunnel-pro

This project is an advanced tunnel management system with enterprise-grade 
features including:

- Modern React dashboard with real-time monitoring
- High-performance Go backend with REST API
- Multi-protocol tunnel support (TCP, UDP, WebSocket, TLS)
- Professional monitoring with Prometheus and Grafana
- Enterprise security features (2FA, RBAC, audit logs)
- Docker and Kubernetes deployment ready
- Comprehensive documentation and testing

For more information, please visit the project repository or contact the author.
