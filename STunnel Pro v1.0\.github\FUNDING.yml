# These are supported funding model platforms

# GitHub Sponsors
github: # Replace with up to 4 GitHub Sponsors-enabled usernames e.g., [user1, user2]

# Patreon
patreon: # Replace with a single Patreon username

# Open Collective
open_collective: # Replace with a single Open Collective username

# Ko-fi
ko_fi: # Replace with a single Ko-fi username

# Tidelift
tidelift: # Replace with a single Tidelift package-name e.g., npm/babel

# Community Bridge
community_bridge: # Replace with a single Community Bridge project-name e.g., cloud-foundry

# Liberapay
liberapay: # Replace with a single Liberapay username

# IssueHunt
issuehunt: # Replace with a single IssueHunt username

# Otechie
otechie: # Replace with a single Otechie username

# LFX Crowdfunding
lfx_crowdfunding: # Replace with a single LFX Crowdfunding project-name e.g., cloud-foundry

# Custom URLs
custom: ['https://coffeebede.com/SalehMonfared']
