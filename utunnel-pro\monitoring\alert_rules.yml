groups:
  - name: utunnel_alerts
    rules:
      # Tunnel Down Alert
      - alert: TunnelDown
        expr: utunnel_tunnel_status == 0
        for: 1m
        labels:
          severity: critical
          service: utunnel
        annotations:
          summary: "Tunnel {{ $labels.tunnel_name }} is down"
          description: "Tunnel {{ $labels.tunnel_name }} (ID: {{ $labels.tunnel_id }}) has been down for more than 1 minute."

      # High Latency Alert
      - alert: HighLatency
        expr: utunnel_tunnel_latency > 100
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High latency detected on tunnel {{ $labels.tunnel_name }}"
          description: "Tunnel {{ $labels.tunnel_name }} has latency of {{ $value }}ms, which is above the 100ms threshold."

      # High Error Rate Alert
      - alert: HighErrorRate
        expr: rate(utunnel_tunnel_errors[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High error rate on tunnel {{ $labels.tunnel_name }}"
          description: "Tunnel {{ $labels.tunnel_name }} has an error rate of {{ $value }} errors/second over the last 5 minutes."

      # No Active Tunnels Alert
      - alert: NoActiveTunnels
        expr: utunnel_active_tunnels == 0
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "No active tunnels"
          description: "There are currently no active tunnels in the system."

      # High Connection Count Alert
      - alert: HighConnectionCount
        expr: utunnel_tunnel_connections > 1000
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High connection count on tunnel {{ $labels.tunnel_name }}"
          description: "Tunnel {{ $labels.tunnel_name }} has {{ $value }} connections, which is above the 1000 threshold."

      # High Bandwidth Usage Alert
      - alert: HighBandwidthUsage
        expr: rate(utunnel_tunnel_bytes_total[5m]) > 100000000  # 100MB/s
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High bandwidth usage on tunnel {{ $labels.tunnel_name }}"
          description: "Tunnel {{ $labels.tunnel_name }} is using {{ $value | humanize }}B/s bandwidth."

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: utunnel_system_cpu_usage > 80
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High CPU usage"
          description: "UTunnel Pro system CPU usage is {{ $value }}%, which is above 80%."

      - alert: HighMemoryUsage
        expr: utunnel_system_memory_usage > 80
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High memory usage"
          description: "UTunnel Pro system memory usage is {{ $value }}%, which is above 80%."

      - alert: LowDiskSpace
        expr: utunnel_system_disk_usage > 90
        for: 5m
        labels:
          severity: critical
          service: utunnel
        annotations:
          summary: "Low disk space"
          description: "UTunnel Pro system disk usage is {{ $value }}%, which is above 90%."

      # Database Connection Alert
      - alert: DatabaseConnectionFailed
        expr: utunnel_database_connected == 0
        for: 1m
        labels:
          severity: critical
          service: utunnel
        annotations:
          summary: "Database connection failed"
          description: "UTunnel Pro cannot connect to the database."

      # Redis Connection Alert
      - alert: RedisConnectionFailed
        expr: utunnel_redis_connected == 0
        for: 1m
        labels:
          severity: critical
          service: utunnel
        annotations:
          summary: "Redis connection failed"
          description: "UTunnel Pro cannot connect to Redis."

      # API Response Time Alert
      - alert: SlowAPIResponse
        expr: histogram_quantile(0.95, rate(utunnel_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "Slow API response time"
          description: "95th percentile API response time is {{ $value }}s, which is above 2 seconds."

      # Failed Login Attempts Alert
      - alert: HighFailedLoginAttempts
        expr: rate(utunnel_failed_login_attempts[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "High number of failed login attempts"
          description: "There have been {{ $value }} failed login attempts per second over the last 5 minutes."

      # WebSocket Connection Alert
      - alert: LowWebSocketConnections
        expr: utunnel_websocket_connections < 1
        for: 10m
        labels:
          severity: info
          service: utunnel
        annotations:
          summary: "No WebSocket connections"
          description: "There are currently no active WebSocket connections for real-time updates."

      # Tunnel Creation Rate Alert
      - alert: HighTunnelCreationRate
        expr: rate(utunnel_tunnels_created_total[5m]) > 10
        for: 5m
        labels:
          severity: info
          service: utunnel
        annotations:
          summary: "High tunnel creation rate"
          description: "{{ $value }} tunnels are being created per second, which might indicate unusual activity."

  - name: utunnel_business_alerts
    rules:
      # User Quota Alerts
      - alert: UserQuotaExceeded
        expr: utunnel_user_tunnel_count > utunnel_user_max_tunnels
        for: 1m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "User {{ $labels.username }} exceeded tunnel quota"
          description: "User {{ $labels.username }} has {{ $labels.tunnel_count }} tunnels, exceeding their quota of {{ $labels.max_tunnels }}."

      - alert: UserBandwidthQuotaExceeded
        expr: rate(utunnel_user_bandwidth_usage[5m]) > utunnel_user_max_bandwidth
        for: 5m
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "User {{ $labels.username }} exceeded bandwidth quota"
          description: "User {{ $labels.username }} is using {{ $value | humanize }}B/s, exceeding their quota."

      # License Alerts
      - alert: LicenseExpiringSoon
        expr: (utunnel_license_expiry_timestamp - time()) < 86400 * 7  # 7 days
        for: 1h
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "License expiring soon"
          description: "UTunnel Pro license will expire in {{ $value | humanizeDuration }}."

      - alert: LicenseExpired
        expr: utunnel_license_expiry_timestamp < time()
        for: 1m
        labels:
          severity: critical
          service: utunnel
        annotations:
          summary: "License expired"
          description: "UTunnel Pro license has expired."

      # Backup Alerts
      - alert: BackupFailed
        expr: utunnel_last_backup_success == 0
        for: 1h
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "Backup failed"
          description: "The last backup attempt failed. Last successful backup was {{ $labels.last_backup_time }}."

      - alert: BackupOverdue
        expr: (time() - utunnel_last_backup_timestamp) > 86400  # 24 hours
        for: 1h
        labels:
          severity: warning
          service: utunnel
        annotations:
          summary: "Backup overdue"
          description: "No successful backup has been completed in the last 24 hours."
