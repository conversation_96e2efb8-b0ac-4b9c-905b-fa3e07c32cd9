apiVersion: apps/v1
kind: Deployment
metadata:
  name: utunnel-pro-backend
  namespace: utunnel-pro
  labels:
    app: utunnel-pro-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: utunnel-pro-backend
  template:
    metadata:
      labels:
        app: utunnel-pro-backend
    spec:
      containers:
      - name: backend
        image: ghcr.io/your-username/utunnel-pro:latest-backend
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_DB
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: utunnel-pro-secrets
              key: JWT_SECRET
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: utunnel-pro-secrets
              key: API_KEY
        - name: TELEGRAM_BOT_TOKEN
          valueFrom:
            secretKeyRef:
              name: utunnel-pro-secrets
              key: TELEGRAM_BOT_TOKEN
              optional: true
        - name: TELEGRAM_CHAT_ID
          valueFrom:
            secretKeyRef:
              name: utunnel-pro-secrets
              key: TELEGRAM_CHAT_ID
              optional: true
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
          readOnly: true
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: config-volume
        configMap:
          name: utunnel-pro-config
      initContainers:
      - name: wait-for-postgres
        image: postgres:15
        command:
        - sh
        - -c
        - |
          until pg_isready -h postgres-service -p 5432 -U utunnel; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
      - name: wait-for-redis
        image: redis:7-alpine
        command:
        - sh
        - -c
        - |
          until redis-cli -h redis-service -p 6379 ping; do
            echo "Waiting for Redis..."
            sleep 2
          done

---
apiVersion: v1
kind: Service
metadata:
  name: utunnel-pro-backend-service
  namespace: utunnel-pro
  labels:
    app: utunnel-pro-backend
spec:
  selector:
    app: utunnel-pro-backend
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: utunnel-pro-backend-hpa
  namespace: utunnel-pro
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: utunnel-pro-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
