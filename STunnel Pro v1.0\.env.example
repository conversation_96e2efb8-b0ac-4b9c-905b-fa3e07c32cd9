# STunnel Pro v1.0 Environment Configuration
# Created by SalehMonfared - https://github.com/SalehMonfared
# Copy this file to .env and update the values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=postgres
DB_PORT=5432
DB_USER=stunnel
DB_PASSWORD=stunnel_password_change_this_in_production
DB_NAME=stunnel_pro

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret - MUST be changed in production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-make-it-long-and-random

# API Key for external integrations
API_KEY=your-api-key-change-this-in-production

# Encryption key for sensitive data
ENCRYPTION_KEY=your-encryption-key-32-characters-long

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment: development, staging, production
ENVIRONMENT=production

# Application URLs
APP_URL=https://your-domain.com
API_URL=https://api.your-domain.com
WS_URL=wss://api.your-domain.com

# Frontend URLs
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/stunnel.crt
SSL_KEY_PATH=/etc/ssl/private/stunnel.key

# Let's Encrypt (if using automatic SSL)
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_DOMAIN=your-domain.com

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
TELEGRAM_ENABLED=false

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=STunnel Pro <<EMAIL>>
SMTP_ENABLED=false

# Webhook Configuration
WEBHOOK_URL=
WEBHOOK_SECRET=
WEBHOOK_ENABLED=false

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Prometheus Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# Grafana Configuration
GRAFANA_PASSWORD=admin_change_this_password
GRAFANA_ENABLED=true

# Log Level: debug, info, warn, error
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# S3 Backup Configuration (optional)
S3_BACKUP_ENABLED=false
S3_BUCKET=stunnel-backups
S3_REGION=us-east-1
S3_ACCESS_KEY=
S3_SECRET_KEY=

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# API Rate Limiting
API_RATE_LIMIT_REQUESTS=1000
API_RATE_LIMIT_WINDOW=3600  # 1 hour

# Login Rate Limiting
LOGIN_RATE_LIMIT_REQUESTS=5
LOGIN_RATE_LIMIT_WINDOW=300  # 5 minutes

# =============================================================================
# USER LIMITS CONFIGURATION
# =============================================================================
# Default User Limits
DEFAULT_MAX_TUNNELS=10
DEFAULT_MAX_BANDWIDTH_MBPS=100
DEFAULT_MAX_CONNECTIONS=1000
DEFAULT_MAX_STORAGE_GB=10
DEFAULT_DAILY_TRANSFER_GB=100
DEFAULT_MONTHLY_TRANSFER_GB=1000

# Premium User Limits
PREMIUM_MAX_TUNNELS=100
PREMIUM_MAX_BANDWIDTH_MBPS=1000
PREMIUM_MAX_CONNECTIONS=10000
PREMIUM_MAX_STORAGE_GB=100
PREMIUM_DAILY_TRANSFER_GB=1000
PREMIUM_MONTHLY_TRANSFER_GB=10000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature Flags
FEATURE_REGISTRATION_ENABLED=true
FEATURE_2FA_ENABLED=true
FEATURE_API_ACCESS_ENABLED=true
FEATURE_PUBLIC_TUNNELS_ENABLED=false
FEATURE_CUSTOM_DOMAINS_ENABLED=false
FEATURE_ANALYTICS_ENABLED=true
FEATURE_AUDIT_LOGS_ENABLED=true

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# GeoIP Service (optional)
GEOIP_ENABLED=false
GEOIP_API_KEY=

# DNS Service (optional)
DNS_PROVIDER=cloudflare
DNS_API_KEY=
DNS_ZONE_ID=

# CDN Configuration (optional)
CDN_ENABLED=false
CDN_URL=https://cdn.your-domain.com

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Development Settings (only for development environment)
DEBUG=false
DEV_MODE=false
HOT_RELOAD=false

# Test Database (for testing)
TEST_DB_HOST=localhost
TEST_DB_PORT=5433
TEST_DB_USER=stunnel_test
TEST_DB_PASSWORD=test_password
TEST_DB_NAME=stunnel_test

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
# Docker Image Tags
BACKEND_IMAGE_TAG=latest
FRONTEND_IMAGE_TAG=latest

# Docker Registry
DOCKER_REGISTRY=ghcr.io/your-username

# =============================================================================
# KUBERNETES CONFIGURATION
# =============================================================================
# Kubernetes Namespace
K8S_NAMESPACE=stunnel-pro

# Kubernetes Storage Class
K8S_STORAGE_CLASS=standard

# Kubernetes Ingress
K8S_INGRESS_CLASS=nginx
K8S_INGRESS_HOST=your-domain.com

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Connection Pool Settings
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_MAX_LIFETIME=5m

REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=2

# Cache Settings
CACHE_TTL=3600  # 1 hour
CACHE_ENABLED=true

# Session Settings
SESSION_TIMEOUT=24h
SESSION_CLEANUP_INTERVAL=1h

# =============================================================================
# SECURITY HEADERS
# =============================================================================
# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true
FRAME_OPTIONS=SAMEORIGIN

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# TIMEZONE AND LOCALIZATION
# =============================================================================
# Timezone
TZ=UTC
DEFAULT_TIMEZONE=UTC

# Language
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,fa,ar,es,fr,de,zh

# =============================================================================
# MAINTENANCE MODE
# =============================================================================
# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1
