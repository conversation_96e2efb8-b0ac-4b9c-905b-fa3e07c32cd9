{"name": "stunnel-pro-dashboard", "version": "1.0.0", "description": "STunnel Pro v1.0 - Advanced Tunnel Management Dashboard", "author": "SalehMonfared <<EMAIL>>", "homepage": "https://github.com/SalehMonfared/stunnel-pro", "funding": {"type": "individual", "url": "https://coffeebede.com/SalehMonfared"}, "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.10.0", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.0", "tailwindcss": "^3.3.0", "@headlessui/react": "^1.7.0", "socket.io-client": "^4.7.0", "react-hook-form": "^7.47.0", "zod": "^3.22.0", "@hookform/resolvers": "^3.3.0", "react-hot-toast": "^2.4.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}