# STunnel Pro v1.0 - Git Ignore Configuration
# Created by SalehMonfared - https://github.com/SalehMonfared

# =============================================================================
# ENVIRONMENT & SECRETS
# =============================================================================
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
*.env
config/secrets.yaml
secrets/
*.key
*.pem
*.crt
*.p12

# =============================================================================
# GO BACKEND
# =============================================================================
# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
stunnel-pro
backend/stunnel-pro

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.txt
coverage.html
*.coverage

# Dependency directories
vendor/
go.work
go.work.sum

# Go workspace file
go.work

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# FRONTEND (React/Next.js)
# =============================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/
frontend/.nuxt/
frontend/.vuepress/dist/
frontend/.serverless/
frontend/.fusebox/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================
# Docker volumes
docker-volumes/
volumes/

# Docker override files
docker-compose.override.yml
docker-compose.local.yml

# =============================================================================
# DATABASES
# =============================================================================
# SQLite
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.dump
*.sql.gz

# Database backups
backups/
*.backup
*.bak

# =============================================================================
# LOGS
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime logs
*.log.*
*.out.*

# =============================================================================
# MONITORING & METRICS
# =============================================================================
# Prometheus data
prometheus-data/
grafana-data/
alertmanager-data/

# Monitoring volumes
monitoring/data/
monitoring/logs/

# =============================================================================
# KUBERNETES
# =============================================================================
# Helm
charts/*/charts/
charts/*/requirements.lock

# Kubernetes secrets
k8s/secrets/
k8s/*/secrets.yaml
k8s/*/secret.yaml

# =============================================================================
# OPERATING SYSTEM
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# EDITORS & IDEs
# =============================================================================
# Visual Studio Code
.vscode/
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
*~
tags
[._]*.un~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# TESTING
# =============================================================================
# Test results
test-results/
test-reports/
coverage/
.nyc_output/

# Jest
jest-coverage/

# =============================================================================
# BUILD ARTIFACTS
# =============================================================================
# Build directories
build/
dist/
out/
target/

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# =============================================================================
# CERTIFICATES & SECURITY
# =============================================================================
# SSL certificates
*.pem
*.key
*.crt
*.cer
*.p7b
*.p7r
*.spc
*.p12
*.pfx

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub

# =============================================================================
# TEMPORARY FILES
# =============================================================================
# Temporary directories
tmp/
temp/
.tmp/
.temp/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*.bak
*.backup
*.orig

# =============================================================================
# ARCHIVES
# =============================================================================
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*.war
*.ear
*.sar
*.class

# =============================================================================
# RUNTIME DATA
# =============================================================================
# Process IDs
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# =============================================================================
# CUSTOM PROJECT IGNORES
# =============================================================================
# Project specific data
data/
uploads/
downloads/
cache/

# Configuration overrides
config.local.yaml
config.override.yaml

# Development files
dev/
development/
.dev/

# Documentation builds
docs/_build/
docs/.doctrees/

# Backup files
*.bak
*.backup
*~

# Lock files (keep package-lock.json and yarn.lock)
# package-lock.json
# yarn.lock

# =============================================================================
# CLOUD & DEPLOYMENT
# =============================================================================
# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# AWS
.aws/

# Google Cloud
.gcloud/
gcloud-service-key.json

# Azure
.azure/

# =============================================================================
# MISCELLANEOUS
# =============================================================================
# Thumbnails
*.thumb

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
