{"name": "stunnel-pro-mobile", "version": "1.0.0", "description": "STunnel Pro v1.0 Mobile App", "author": "SalehMonfared", "homepage": "https://github.com/SalehMonfared/stunnel-pro", "funding": {"type": "individual", "url": "https://coffeebede.com/SalehMonfared"}, "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace STunnelPro.xcworkspace -scheme STunnelPro -configuration Release -destination generic/platform=iOS -archivePath STunnelPro.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "@tanstack/react-query": "^5.0.0", "react-native-async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.14.0", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.2.0", "react-native-share": "^9.4.1", "react-native-device-info": "^10.11.0", "react-native-network-info": "^5.2.1", "react-native-background-job": "^1.2.0", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/analytics": "^18.6.1", "react-native-websocket": "^1.0.2", "axios": "^1.6.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-hook-form": "^7.47.0", "yup": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@types/lodash": "^4.14.200", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}