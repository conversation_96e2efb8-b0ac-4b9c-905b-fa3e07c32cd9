# STunnel Pro v1.0 - Docker Compose Configuration
# Advanced Tunnel Management System
# Created by SalehMonfared - https://github.com/SalehMonfared

version: '3.8'

services:
  # Frontend (Next.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8080
      - NEXT_PUBLIC_WS_URL=ws://backend:8080
    depends_on:
      - backend
    networks:
      - utunnel-network
    restart: unless-stopped

  # Backend (Go)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
      - "9090:9090"  # Prometheus metrics
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=utunnel
      - DB_PASSWORD=utunnel_password
      - DB_NAME=utunnel_pro
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - LOG_LEVEL=info
      - GIN_MODE=release
    depends_on:
      - postgres
      - redis
    networks:
      - utunnel-network
    restart: unless-stopped
    volumes:
      - ./backend/configs:/app/configs
      - ./backend/logs:/app/logs
      - tunnel-data:/app/data

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=utunnel
      - POSTGRES_PASSWORD=utunnel_password
      - POSTGRES_DB=utunnel_pro
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - utunnel-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - utunnel-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - utunnel-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - utunnel-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - utunnel-network
    restart: unless-stopped

  # Tunnel Manager (Core Service)
  tunnel-manager:
    build:
      context: ./tunnel-core
      dockerfile: Dockerfile
    environment:
      - CONFIG_PATH=/app/config
      - LOG_LEVEL=info
      - METRICS_PORT=8081
    volumes:
      - tunnel-data:/app/data
      - ./tunnel-core/config:/app/config
    networks:
      - utunnel-network
      - tunnel-network
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
    privileged: true

  # Log Aggregator (ELK Stack - Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - utunnel-network
    restart: unless-stopped

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - utunnel-network
    restart: unless-stopped

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    depends_on:
      - elasticsearch
    networks:
      - utunnel-network
    restart: unless-stopped

  # Backup Service
  backup:
    image: postgres:15-alpine
    environment:
      - PGPASSWORD=utunnel_password
    volumes:
      - ./backups:/backups
      - backup-scripts:/scripts
    command: |
      sh -c '
        while true; do
          echo "Creating backup at $$(date)"
          pg_dump -h postgres -U utunnel -d utunnel_pro > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql
          find /backups -name "backup_*.sql" -mtime +7 -delete
          sleep 86400
        done
      '
    depends_on:
      - postgres
    networks:
      - utunnel-network
    restart: unless-stopped

networks:
  utunnel-network:
    driver: bridge
  tunnel-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  prometheus-data:
  grafana-data:
  elasticsearch-data:
  tunnel-data:
  backup-scripts:
