apiVersion: v1
kind: Secret
metadata:
  name: utunnel-pro-secrets
  namespace: utunnel-pro
type: Opaque
data:
  # Base64 encoded values
  # To encode: echo -n "your-value" | base64
  
  # Database password (default: utunnel_password)
  DB_PASSWORD: dXR1bm5lbF9wYXNzd29yZA==
  
  # JWT secret (default: your-super-secret-jwt-key-change-this-in-production)
  JWT_SECRET: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1jaGFuZ2UtdGhpcy1pbi1wcm9kdWN0aW9u
  
  # API key (default: your-api-key-change-this)
  API_KEY: eW91ci1hcGkta2V5LWNoYW5nZS10aGlz
  
  # Telegram bot token (optional)
  TELEGRAM_BOT_TOKEN: ""
  
  # Telegram chat ID (optional)
  TELEGRAM_CHAT_ID: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: utunnel-pro
type: Opaque
data:
  # PostgreSQL credentials
  POSTGRES_USER: dXR1bm5lbA==  # utunnel
  POSTGRES_PASSWORD: dXR1bm5lbF9wYXNzd29yZA==  # utunnel_password
  POSTGRES_DB: dXR1bm5lbF9wcm8=  # utunnel_pro
